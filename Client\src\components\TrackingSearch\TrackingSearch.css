.tracking-search-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  position: relative;
}

.tracking-search-form {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
}

.tracking-input-wrapper {
  flex: 1;
  position: relative;
  background: white;
  border-radius: 16px;
  border: 2px solid #E2E8F0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.tracking-input-wrapper:hover {
  border-color: #CBD5E0;
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

.tracking-input-wrapper:focus-within {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 4px rgba(43, 78, 255, 0.15);
}

.tracking-input {
  width: 100%;
  padding: 16px 20px;
  font-size: 1.1rem;
  border: none;
  background: transparent;
  color: #1a202c;
  border-radius: 16px;
  transition: all 0.3s ease;
}

.tracking-input::placeholder {
  color: #a0aec0;
}

.tracking-input:focus {
  outline: none;
}

.tracking-button {
  padding: 14px 28px;
  background: var(--accent-color);
  color: white;
  border: 2px solid var(--accent-color);
  border-radius: 12px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.tracking-button:hover {
  background: #1a3ccc; /* Version plus foncée du bleu accent */
  border-color: #1a3ccc;
  color: white;
  box-shadow: 0 4px 12px rgba(43, 78, 255, 0.2);
}

.tracking-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(43, 78, 255, 0.15);
}

.tracking-error {
  color: #dc2626;
  margin-top: 1rem;
  text-align: center;
  font-size: 0.95rem;
  padding: 0.75rem 1rem;
  background-color: #fef2f2;
  border-radius: 12px;
  border: 1px solid #fee2e2;
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.08);
}

@media (max-width: 480px) {
  .tracking-search-form {
    flex-direction: column;
    gap: 8px;
  }

  .tracking-input {
    padding: 14px 16px;
    font-size: 1rem;
  }
  
  .tracking-button {
    width: 100%;
    padding: 14px 20px;
    font-size: 0.95rem;
  }
}










