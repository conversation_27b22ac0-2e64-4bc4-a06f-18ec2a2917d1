.map-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Ensure the Leaflet map container takes full height */
.leaflet-container {
  width: 100%;
  height: 400px;
  z-index: 1;
}

/* Fix for Leaflet marker icon issues */
.leaflet-marker-icon,
.leaflet-marker-shadow {
  filter: drop-shadow(0 2px 2px rgba(0,0,0,0.2));
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: none;
  background: none;
  cursor: pointer;
  color: var(--primary-color);
}

.back-button:hover {
  background-color: rgba(0,0,0,0.04);
  border-radius: 4px;
}