.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  transition: height 0.3s ease;
}

.stats-card:hover::before {
  height: 6px;
}

.stats-card:nth-child(2)::before {
  background: var(--gradient-secondary);
}

.stats-card:nth-child(3)::before {
  background: linear-gradient(135deg, var(--purple-color) 0%, var(--primary-color) 100%);
}

.stats-card:nth-child(4)::before {
  background: linear-gradient(135deg, var(--accent-color) 0%, #ea580c 100%);
}

/* Animation pour les cartes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-card {
  animation: fadeInUp 0.6s ease forwards;
}

.stats-card:nth-child(1) {
  animation-delay: 0.1s;
}

.stats-card:nth-child(2) {
  animation-delay: 0.2s;
}

.stats-card:nth-child(3) {
  animation-delay: 0.3s;
}

.stats-card:nth-child(4) {
  animation-delay: 0.4s;
}

/* Effet de pulsation pour les icônes */
.stats-card:hover .MuiSvgIcon-root {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .stats-card {
    margin-bottom: 1rem;
  }
}
