.back-link {
  display: inline-flex;
  align-items: center;
  text-decoration: none;
  color: #4A5568;
  font-size: 16.5px;
  margin-bottom: 0;
  cursor: pointer;
  border: none;
  background: none;
  padding: 4px 0;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  transition: all 0.2s ease-in-out;
}

.back-link:hover {
  color: var(--primary-color);
  transform: translateX(0px);
}

.back-link svg {
  width: 26px;
  height: 26px;
  margin-right: 6px;
  position: relative;
  top: 1px;
}

.back-link-text {
  font-weight: 500;
  letter-spacing: -0.3px;
  position: relative;
  top: 1px;
  line-height: 1.2;
}

/* Supprimer les styles du bouton précédent qui ne sont plus nécessaires */
.back-button {
  display: none;
}

.notification-body {
  background-color: #f9f9f9;
  padding: 16px;
  border-radius: 8px;
  line-height: 1.6;
}

.notification-body h2 {
  color: var(--primary-color);
  margin-top: 0;
  font-size: 1.5rem;
}

.notification-body ul {
  padding-left: 20px;
}

.notification-body li {
  margin-bottom: 8px;
}

.notification-body strong {
  font-weight: 600;
}
