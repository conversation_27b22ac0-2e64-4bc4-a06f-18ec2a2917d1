{"name": "horizon", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0 --fix", "preview": "vite preview", "predeploy": "vite build && cp ./dist/index.html ./dist/404.html", "deploy": "gh-pages -d dist", "start": "react-scripts start"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^4.1.3", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.4.7", "@mui/x-data-grid": "^7.14.0", "@mui/x-date-pickers": "^7.15.0", "@react-google-maps/api": "^2.20.6", "@tanstack/react-query": "^5.68.0", "@types/google.maps": "^3.58.1", "@types/html-docx-js": "^0.3.4", "@types/leaflet": "^1.9.17", "@types/recharts": "^1.8.29", "apexcharts": "^4.7.0", "axios": "^1.8.3", "bootstrap": "^5.3.3", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "echarts-for-react": "^3.0.2", "file-saver": "^2.0.5", "html-docx-js": "^0.3.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lucide-react": "^0.482.0", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-leaflet": "^4.2.1", "react-qr-code": "^2.0.15", "react-router-dom": "^6.26.0", "react-toastify": "^11.0.5", "recharts": "^2.15.3", "resize-observer-polyfill": "^1.5.1", "swiper": "^11.1.12", "xlsx": "^0.18.5", "yup": "^1.6.1"}, "devDependencies": {"@iconify/react": "^5.0.2", "@types/file-saver": "^2.0.7", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "gh-pages": "^6.1.1", "postcss": "^8.5.3", "prettier": "^3.3.3", "tailwindcss": "^4.0.15", "typescript": "^5.2.2", "vite": "^5.3.4", "vite-plugin-checker": "^0.7.2", "vite-tsconfig-paths": "^4.3.2"}}