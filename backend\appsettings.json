{"ConnectionStrings": {"DefaultConnection": "Server=PASSPOUSSI\\MSSQLSERVER2022;Database=AxiaLivraison;Trusted_Connection=True;TrustServerCertificate=True"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "Port": "587", "Username": "<EMAIL>", "Password": "jmqyonhfljspelcz", "SenderEmail": "<EMAIL>", "SenderName": "<PERSON><PERSON>"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "AxiaLivraisonAPI.Services.EmailService": "Debug"}}, "FileSettings": {"UploadPath": "Uploads"}, "AllowedHosts": "*", "JwtSettings": {"SecretKey": "AxiaLivraison2024SecretKeyForJWTTokenGeneration!@#$%^&*()_+", "Issuer": "AxiaLivraisonAPI", "Audience": "AxiaLivraisonClients", "AccessTokenExpiryMinutes": "60", "RefreshTokenExpiryDays": "7"}}